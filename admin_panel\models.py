"""
Admin panel models for system monitoring, audit trails, and compliance
"""

import uuid
from typing import TYPE_CHECKING
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import <PERSON>ric<PERSON><PERSON>ign<PERSON><PERSON>
from decimal import Decimal

if TYPE_CHECKING:
    from accounts.models import CustomUser

User = get_user_model()


class AuditLog(models.Model):
    """Comprehensive audit trail for all system activities"""

    ACTION_TYPES = [
        ('create', 'Create'),
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('bet_placed', 'Bet Placed'),
        ('bet_settled', 'Bet Settled'),
        ('deposit', 'Deposit'),
        ('withdrawal', 'Withdrawal'),
        ('odds_change', 'Odds Change'),
        ('user_suspended', 'User Suspended'),
        ('user_activated', 'User Activated'),
        ('admin_action', 'Admin Action'),
        ('security_event', 'Security Event'),
        ('system_event', 'System Event'),
    ]

    RISK_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Action details
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    description = models.TextField()
    risk_level = models.CharField(max_length=10, choices=RISK_LEVELS, default='low')

    # User and session info
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='audit_logs')
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True, default='')
    session_key = models.CharField(max_length=40, blank=True)

    # Related object (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, null=True, blank=True)
    object_id = models.CharField(max_length=255, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')

    # Additional data
    old_values = models.JSONField(default=dict, blank=True)
    new_values = models.JSONField(default=dict, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['action_type', 'created_at']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['risk_level', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
        ]

    def __str__(self):
        return f"{self.action_type} by {self.user or 'System'} at {self.created_at}"


class SuspiciousActivity(models.Model):
    """Detection and tracking of suspicious user activities"""

    ACTIVITY_TYPES = [
        ('multiple_accounts', 'Multiple Accounts'),
        ('unusual_betting', 'Unusual Betting Pattern'),
        ('rapid_deposits', 'Rapid Deposits'),
        ('large_withdrawals', 'Large Withdrawals'),
        ('ip_anomaly', 'IP Address Anomaly'),
        ('device_anomaly', 'Device Anomaly'),
        ('time_anomaly', 'Time Pattern Anomaly'),
        ('odds_manipulation', 'Odds Manipulation Attempt'),
        ('bonus_abuse', 'Bonus Abuse'),
        ('collusion', 'Potential Collusion'),
    ]

    STATUS_CHOICES = [
        ('detected', 'Detected'),
        ('investigating', 'Under Investigation'),
        ('confirmed', 'Confirmed'),
        ('false_positive', 'False Positive'),
        ('resolved', 'Resolved'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Activity details
    activity_type = models.CharField(max_length=30, choices=ACTIVITY_TYPES)
    description = models.TextField()
    severity_score = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='detected')

    # User and related data
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='suspicious_activities')
    related_users = models.ManyToManyField(User, blank=True, related_name='related_suspicious_activities')

    # Detection details
    detection_rules = models.JSONField(default=list)
    evidence_data = models.JSONField(default=dict)
    risk_factors = models.JSONField(default=list)

    # Investigation
    assigned_to = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_investigations',
        limit_choices_to={'is_staff': True}
    )
    investigation_notes = models.TextField(blank=True)
    resolution_action = models.TextField(blank=True)

    # Timestamps
    detected_at = models.DateTimeField(auto_now_add=True)
    investigated_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-detected_at']
        indexes = [
            models.Index(fields=['activity_type', 'status']),
            models.Index(fields=['user', 'detected_at']),
            models.Index(fields=['severity_score', 'detected_at']),
            models.Index(fields=['status', 'detected_at']),
        ]

    def __str__(self):
        user_identifier = getattr(self.user, 'phone_number', 'Unknown User') if self.user else "Unknown User"
        return f"{self.activity_type} - {user_identifier} ({self.status})"

    @property
    def is_high_risk(self):
        return self.severity_score >= Decimal('7.0')

    def assign_investigator(self, investigator: "CustomUser"):
        """Assign an investigator to this case"""
        self.assigned_to = investigator
        self.status = 'investigating'
        self.investigated_at = timezone.now()
        self.save(update_fields=['assigned_to', 'status', 'investigated_at'])

    def resolve_case(self, resolution_action: str, resolved_by: "CustomUser"):
        """Resolve the suspicious activity case"""
        self.resolution_action = resolution_action
        self.status = 'resolved'
        self.resolved_at = timezone.now()
        # Track who resolved the case
        self.assigned_to = resolved_by
        self.save(update_fields=['resolution_action', 'status', 'resolved_at', 'assigned_to'])


class SystemMonitoring(models.Model):
    """System performance and health monitoring"""

    METRIC_TYPES = [
        ('cpu_usage', 'CPU Usage'),
        ('memory_usage', 'Memory Usage'),
        ('disk_usage', 'Disk Usage'),
        ('database_connections', 'Database Connections'),
        ('active_users', 'Active Users'),
        ('bet_volume', 'Betting Volume'),
        ('transaction_volume', 'Transaction Volume'),
        ('error_rate', 'Error Rate'),
        ('response_time', 'Response Time'),
        ('uptime', 'System Uptime'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Metric details
    metric_type = models.CharField(max_length=30, choices=METRIC_TYPES)
    metric_value = models.DecimalField(max_digits=15, decimal_places=4)
    metric_unit = models.CharField(max_length=20, default='count')

    # Thresholds and alerts
    warning_threshold = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    critical_threshold = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    is_alert = models.BooleanField(default=False)
    alert_level = models.CharField(max_length=10, choices=[('warning', 'Warning'), ('critical', 'Critical')], blank=True)

    # Additional data
    metadata = models.JSONField(default=dict, blank=True)

    # Timestamps
    recorded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-recorded_at']
        indexes = [
            models.Index(fields=['metric_type', 'recorded_at']),
            models.Index(fields=['is_alert', 'recorded_at']),
            models.Index(fields=['alert_level', 'recorded_at']),
        ]

    def __str__(self):
        return f"{self.metric_type}: {self.metric_value} {self.metric_unit}"

    def check_thresholds(self):
        """Check if metric value exceeds thresholds"""
        if self.critical_threshold and self.metric_value >= self.critical_threshold:
            self.is_alert = True
            self.alert_level = 'critical'
        elif self.warning_threshold and self.metric_value >= self.warning_threshold:
            self.is_alert = True
            self.alert_level = 'warning'
        else:
            self.is_alert = False
            self.alert_level = ''

        self.save(update_fields=['is_alert', 'alert_level'])


class RegulatoryReport(models.Model):
    """Automated regulatory reporting"""

    REPORT_TYPES = [
        ('daily_summary', 'Daily Summary'),
        ('weekly_summary', 'Weekly Summary'),
        ('monthly_summary', 'Monthly Summary'),
        ('suspicious_activity', 'Suspicious Activity Report'),
        ('large_transactions', 'Large Transactions Report'),
        ('user_verification', 'User Verification Report'),
        ('betting_patterns', 'Betting Patterns Report'),
        ('financial_summary', 'Financial Summary'),
        ('compliance_audit', 'Compliance Audit'),
        ('custom', 'Custom Report'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('generating', 'Generating'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('submitted', 'Submitted'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Report details
    report_type = models.CharField(max_length=30, choices=REPORT_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)

    # Date range
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()

    # Generation details
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='generated_reports')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Report data
    report_data = models.JSONField(default=dict, blank=True)
    file_path = models.CharField(max_length=500, blank=True)
    file_size = models.PositiveIntegerField(default=0)

    # Submission details
    submitted_to = models.CharField(max_length=200, blank=True)
    submission_reference = models.CharField(max_length=100, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    generated_at = models.DateTimeField(null=True, blank=True)
    submitted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['report_type', 'status']),
            models.Index(fields=['generated_by', 'created_at']),
            models.Index(fields=['start_date', 'end_date']),
        ]

    def __str__(self):
        return f"{self.title} ({self.start_date.date()} - {self.end_date.date()})"

    def mark_as_generating(self):
        """Mark report as being generated"""
        self.status = 'generating'
        self.save(update_fields=['status'])

    def mark_as_completed(self, file_path: str, file_size: int):
        """Mark report as completed"""
        self.status = 'completed'
        self.file_path = file_path
        self.file_size = file_size
        self.generated_at = timezone.now()
        self.save(update_fields=['status', 'file_path', 'file_size', 'generated_at'])

    def mark_as_submitted(self, submitted_to: str, reference: str = ''):
        """Mark report as submitted"""
        self.status = 'submitted'
        self.submitted_to = submitted_to
        self.submission_reference = reference
        self.submitted_at = timezone.now()
        self.save(update_fields=['status', 'submitted_to', 'submission_reference', 'submitted_at'])


class UserAction(models.Model):
    """Track administrative actions on user accounts"""

    ACTION_TYPES = [
        ('suspend', 'Account Suspended'),
        ('activate', 'Account Activated'),
        ('verify', 'Account Verified'),
        ('limit_set', 'Betting Limit Set'),
        ('limit_removed', 'Betting Limit Removed'),
        ('bonus_granted', 'Bonus Granted'),
        ('bonus_revoked', 'Bonus Revoked'),
        ('balance_adjustment', 'Balance Adjustment'),
        ('document_approved', 'Document Approved'),
        ('document_rejected', 'Document Rejected'),
        ('note_added', 'Note Added'),
        ('warning_issued', 'Warning Issued'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Action details
    action_type = models.CharField(max_length=30, choices=ACTION_TYPES)
    description = models.TextField()
    reason = models.TextField()

    # Users involved
    target_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='admin_actions_received')
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='admin_actions_performed')

    # Action data
    previous_values = models.JSONField(default=dict, blank=True)
    new_values = models.JSONField(default=dict, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    # Timestamps
    performed_at = models.DateTimeField(auto_now_add=True)
    effective_until = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-performed_at']
        indexes = [
            models.Index(fields=['target_user', 'performed_at']),
            models.Index(fields=['action_type', 'performed_at']),
            models.Index(fields=['performed_by', 'performed_at']),
        ]

    def __str__(self):
        target_identifier = getattr(self.target_user, 'phone_number', 'Unknown User') if self.target_user else "Unknown User"
        performed_by_identifier = getattr(self.performed_by, 'phone_number', 'System') if self.performed_by else "System"
        return f"{self.action_type} on {target_identifier} by {performed_by_identifier}"


class SystemAlert(models.Model):
    """System-wide alerts and notifications for administrators"""

    ALERT_TYPES = [
        ('security', 'Security Alert'),
        ('performance', 'Performance Alert'),
        ('compliance', 'Compliance Alert'),
        ('financial', 'Financial Alert'),
        ('technical', 'Technical Alert'),
        ('user_activity', 'User Activity Alert'),
        ('system_error', 'System Error'),
        ('maintenance', 'Maintenance Alert'),
    ]

    SEVERITY_LEVELS = [
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Alert details
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS)
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Status and handling
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    acknowledged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_alerts'
    )
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_alerts'
    )

    # Additional data
    source_system = models.CharField(max_length=100, blank=True)
    error_code = models.CharField(max_length=50, blank=True)
    metadata = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['alert_type', 'severity', 'status']),
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['severity', 'created_at']),
        ]

    def __str__(self):
        return f"{self.severity.upper()}: {self.title}"

    def acknowledge(self, user: "CustomUser"):
        """Acknowledge the alert"""
        self.status = 'acknowledged'
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        self.save(update_fields=['status', 'acknowledged_by', 'acknowledged_at'])

    def resolve(self, user: "CustomUser"):
        """Resolve the alert"""
        self.status = 'resolved'
        self.resolved_by = user
        self.resolved_at = timezone.now()
        self.save(update_fields=['status', 'resolved_by', 'resolved_at'])

    @property
    def is_critical(self):
        return self.severity == 'critical'

    @property
    def age_in_hours(self):
        """Calculate alert age in hours"""
        delta = timezone.now() - self.created_at
        return delta.total_seconds() / 3600


class AdminSettings(models.Model):
    """System-wide administrative settings and configurations"""

    SETTING_TYPES = [
        ('system', 'System Setting'),
        ('security', 'Security Setting'),
        ('betting', 'Betting Setting'),
        ('payment', 'Payment Setting'),
        ('notification', 'Notification Setting'),
        ('compliance', 'Compliance Setting'),
        ('maintenance', 'Maintenance Setting'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Setting details
    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    setting_type = models.CharField(max_length=20, choices=SETTING_TYPES)
    description = models.TextField()

    # Validation and constraints
    data_type = models.CharField(
        max_length=20,
        choices=[
            ('string', 'String'),
            ('integer', 'Integer'),
            ('decimal', 'Decimal'),
            ('boolean', 'Boolean'),
            ('json', 'JSON'),
            ('datetime', 'DateTime'),
        ],
        default='string'
    )
    validation_rules = models.JSONField(default=dict, blank=True)

    # Management
    is_active = models.BooleanField(default=True)
    is_sensitive = models.BooleanField(default=False)  # Hide value in admin
    requires_restart = models.BooleanField(default=False)

    # Change tracking
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_settings')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='updated_settings')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['setting_type', 'key']
        indexes = [
            models.Index(fields=['setting_type', 'is_active']),
            models.Index(fields=['key']),
        ]

    def __str__(self):
        return f"{self.key}: {self.value[:50]}{'...' if len(self.value) > 50 else ''}"

    def get_typed_value(self):
        """Return value converted to appropriate data type"""
        if self.data_type == 'integer':
            return int(self.value)
        elif self.data_type == 'decimal':
            return Decimal(self.value)
        elif self.data_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.data_type == 'json':
            import json
            return json.loads(self.value)
        elif self.data_type == 'datetime':
            from django.utils.dateparse import parse_datetime
            return parse_datetime(self.value)
        else:
            return self.value

    def set_typed_value(self, value):
        """Set value from typed input"""
        if self.data_type == 'json':
            import json
            self.value = json.dumps(value)
        elif self.data_type == 'datetime':
            self.value = value.isoformat() if hasattr(value, 'isoformat') else str(value)
        else:
            self.value = str(value)


class MaintenanceWindow(models.Model):
    """Scheduled maintenance windows"""

    MAINTENANCE_TYPES = [
        ('system_update', 'System Update'),
        ('database_maintenance', 'Database Maintenance'),
        ('security_patch', 'Security Patch'),
        ('performance_optimization', 'Performance Optimization'),
        ('feature_deployment', 'Feature Deployment'),
        ('emergency_fix', 'Emergency Fix'),
    ]

    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('failed', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Maintenance details
    title = models.CharField(max_length=200)
    description = models.TextField()
    maintenance_type = models.CharField(max_length=30, choices=MAINTENANCE_TYPES)

    # Scheduling
    scheduled_start = models.DateTimeField()
    scheduled_end = models.DateTimeField()
    actual_start = models.DateTimeField(null=True, blank=True)
    actual_end = models.DateTimeField(null=True, blank=True)

    # Status and management
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='assigned_maintenance')

    # Impact and notifications
    affects_betting = models.BooleanField(default=True)
    affects_payments = models.BooleanField(default=False)
    affects_registration = models.BooleanField(default=False)
    notify_users = models.BooleanField(default=True)

    # Progress tracking
    progress_notes = models.TextField(blank=True)
    completion_notes = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-scheduled_start']
        indexes = [
            models.Index(fields=['status', 'scheduled_start']),
            models.Index(fields=['scheduled_start', 'scheduled_end']),
        ]

    def __str__(self):
        return f"{self.title} - {self.scheduled_start.date()}"

    @property
    def is_active(self):
        """Check if maintenance is currently active"""
        now = timezone.now()
        return (self.status == 'in_progress' or
                (self.status == 'scheduled' and
                 self.scheduled_start <= now <= self.scheduled_end))

    @property
    def duration_planned(self):
        """Planned duration of maintenance"""
        return self.scheduled_end - self.scheduled_start

    @property
    def duration_actual(self):
        """Actual duration of maintenance"""
        if self.actual_start and self.actual_end:
            return self.actual_end - self.actual_start
        return None

    def start_maintenance(self, user: "CustomUser"):
        """Start the maintenance window"""
        self.status = 'in_progress'
        self.actual_start = timezone.now()
        self.assigned_to = user
        self.save(update_fields=['status', 'actual_start', 'assigned_to'])

    def complete_maintenance(self, completion_notes: str = ''):
        """Complete the maintenance window"""
        self.status = 'completed'
        self.actual_end = timezone.now()
        self.completion_notes = completion_notes
        self.save(update_fields=['status', 'actual_end', 'completion_notes'])
