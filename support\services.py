"""
Support services for ticket management, FAQ, and live chat
"""

import logging
from typing import Dict, List, Optional, Tuple, TYPE_CHECKING
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db.models import Q, Count, Avg
from django.core.mail import send_mail
from django.conf import settings

from .models import (
    SupportTicket, TicketResponse, SupportCategory,
    FAQArticle, FAQCategory, ChatSession, ChatMessage,
    SupportNotification
)

if TYPE_CHECKING:
    from accounts.models import CustomUser

logger = logging.getLogger(__name__)
User = get_user_model()


class SupportTicketService:
    """Service for managing support tickets"""
    
    def create_ticket(self, user: "CustomUser", subject: str, description: str,
                     category_id: str = None, priority: str = 'normal') -> SupportTicket:
        """
        Create a new support ticket
        
        Args:
            user: User creating the ticket
            subject: Ticket subject
            description: Ticket description
            category_id: Optional category ID
            priority: Ticket priority
        
        Returns:
            SupportTicket: Created ticket
        """
        try:
            with transaction.atomic():
                # Get category if provided
                category = None
                if category_id:
                    try:
                        category = SupportCategory.objects.get(id=category_id, is_active=True)
                    except SupportCategory.DoesNotExist:
                        logger.warning(f"Category {category_id} not found")
                
                # Create ticket
                ticket = SupportTicket.objects.create(
                    user=user,
                    subject=subject,
                    description=description,
                    category=category,
                    priority=priority
                )
                
                # Create notification for staff
                self._create_ticket_notification(ticket, 'ticket_created')
                
                # Send email notification to staff
                self._send_ticket_email_notification(ticket, 'created')
                
                logger.info(f"Ticket created: {ticket.ticket_number} by user {user.id}")
                return ticket
                
        except Exception as e:
            logger.error(f"Error creating ticket: {e}")
            raise
    
    def add_response(self, ticket: SupportTicket, user: "CustomUser", message: str,
                    is_internal_note: bool = False, attachment=None) -> TicketResponse:
        """
        Add a response to a ticket
        
        Args:
            ticket: Ticket to respond to
            user: User adding the response
            message: Response message
            is_internal_note: Whether this is an internal note
            attachment: Optional file attachment
        
        Returns:
            TicketResponse: Created response
        """
        try:
            with transaction.atomic():
                # Create response
                response = TicketResponse.objects.create(
                    ticket=ticket,
                    user=user,
                    message=message,
                    is_internal_note=is_internal_note,
                    attachment=attachment
                )
                
                # Update ticket status if needed
                if user.is_staff and ticket.status == 'open':
                    ticket.status = 'in_progress'
                elif not user.is_staff and ticket.status == 'waiting_customer':
                    ticket.status = 'in_progress'
                
                ticket.save(update_fields=['status'])
                
                # Create notification
                notification_type = 'ticket_response'
                recipient = ticket.user if user.is_staff else ticket.assigned_to
                
                if recipient and not is_internal_note:
                    self._create_response_notification(response, notification_type, recipient)
                    self._send_response_email_notification(response, recipient)
                
                logger.info(f"Response added to ticket {ticket.ticket_number} by user {user.id}")
                return response
                
        except Exception as e:
            logger.error(f"Error adding response to ticket {ticket.id}: {e}")
            raise
    
    def assign_ticket(self, ticket: SupportTicket, agent: "CustomUser", assigned_by: "CustomUser" = None) -> bool:
        """
        Assign a ticket to an agent
        
        Args:
            ticket: Ticket to assign
            agent: Agent to assign to
            assigned_by: User who made the assignment
        
        Returns:
            bool: Success status
        """
        try:
            if not agent.is_staff:
                raise ValueError("Only staff members can be assigned tickets")
            
            ticket.assigned_to = agent
            ticket.status = 'in_progress'
            ticket.save(update_fields=['assigned_to', 'status'])
            
            # Create notification
            self._create_assignment_notification(ticket, agent, assigned_by)
            
            logger.info(f"Ticket {ticket.ticket_number} assigned to {agent.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error assigning ticket {ticket.id}: {e}")
            return False
    
    def close_ticket(self, ticket: SupportTicket, resolved_by: "CustomUser", resolution: str = '') -> bool:
        """
        Close a support ticket
        
        Args:
            ticket: Ticket to close
            resolved_by: User closing the ticket
            resolution: Resolution description
        
        Returns:
            bool: Success status
        """
        try:
            ticket.close_ticket(resolved_by=resolved_by, resolution=resolution)
            
            # Create notification
            self._create_ticket_notification(ticket, 'ticket_updated')
            
            # Send email notification
            self._send_ticket_email_notification(ticket, 'closed')
            
            logger.info(f"Ticket {ticket.ticket_number} closed by {resolved_by.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error closing ticket {ticket.id}: {e}")
            return False
    
    def escalate_ticket(self, ticket: SupportTicket, escalated_by: User = None) -> bool:
        """
        Escalate a support ticket
        
        Args:
            ticket: Ticket to escalate
            escalated_by: User who escalated the ticket
        
        Returns:
            bool: Success status
        """
        try:
            ticket.escalate_ticket()
            
            # Create notification for management
            self._create_escalation_notification(ticket, escalated_by)
            
            logger.info(f"Ticket {ticket.ticket_number} escalated")
            return True
            
        except Exception as e:
            logger.error(f"Error escalating ticket {ticket.id}: {e}")
            return False
    
    def get_ticket_statistics(self, user: User = None) -> Dict:
        """
        Get ticket statistics
        
        Args:
            user: Optional user to filter by
        
        Returns:
            dict: Statistics
        """
        try:
            tickets = SupportTicket.objects.all()
            if user and not user.is_staff:
                tickets = tickets.filter(user=user)
            
            stats = {
                'total_tickets': tickets.count(),
                'open_tickets': tickets.filter(status__in=['open', 'in_progress', 'waiting_customer']).count(),
                'closed_tickets': tickets.filter(status='closed').count(),
                'escalated_tickets': tickets.filter(status='escalated').count(),
                'avg_response_time': 0,
                'avg_resolution_time': 0,
            }
            
            # Calculate average response time
            tickets_with_responses = tickets.filter(responses__is_staff_response=True).distinct()
            if tickets_with_responses.exists():
                response_times = [t.response_time for t in tickets_with_responses if t.response_time]
                if response_times:
                    stats['avg_response_time'] = sum(response_times) / len(response_times)
            
            # Calculate average resolution time
            closed_tickets = tickets.filter(status='closed', resolved_at__isnull=False)
            if closed_tickets.exists():
                resolution_times = []
                for ticket in closed_tickets:
                    delta = ticket.resolved_at - ticket.created_at
                    resolution_times.append(delta.total_seconds() / 3600)  # Hours
                
                if resolution_times:
                    stats['avg_resolution_time'] = sum(resolution_times) / len(resolution_times)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting ticket statistics: {e}")
            return {'error': str(e)}
    
    def search_tickets(self, query: str, user: "CustomUser" = None, status: str = None) -> List[SupportTicket]:
        """
        Search tickets by query
        
        Args:
            query: Search query
            user: Optional user filter
            status: Optional status filter
        
        Returns:
            List[SupportTicket]: Matching tickets
        """
        try:
            tickets = SupportTicket.objects.all()
            
            if user and not user.is_staff:
                tickets = tickets.filter(user=user)
            
            if status:
                tickets = tickets.filter(status=status)
            
            if query:
                tickets = tickets.filter(
                    Q(ticket_number__icontains=query) |
                    Q(subject__icontains=query) |
                    Q(description__icontains=query) |
                    Q(responses__message__icontains=query)
                ).distinct()
            
            return tickets.order_by('-created_at')
            
        except Exception as e:
            logger.error(f"Error searching tickets: {e}")
            return []
    
    def _create_ticket_notification(self, ticket: SupportTicket, notification_type: str):
        """Create notification for ticket events"""
        try:
            # Notify staff members
            staff_users = User.objects.filter(is_staff=True, is_active=True)
            
            for staff_user in staff_users:
                SupportNotification.objects.create(
                    recipient=staff_user,
                    notification_type=notification_type,
                    title=f"New Ticket: {ticket.ticket_number}",
                    message=f"A new support ticket has been created: {ticket.subject}",
                    ticket=ticket
                )
                
        except Exception as e:
            logger.error(f"Error creating ticket notification: {e}")
    
    def _create_response_notification(self, response: TicketResponse, notification_type: str, recipient: "CustomUser"):
        """Create notification for ticket responses"""
        try:
            SupportNotification.objects.create(
                recipient=recipient,
                notification_type=notification_type,
                title=f"Response to Ticket: {response.ticket.ticket_number}",
                message=f"A new response has been added to your ticket: {response.ticket.subject}",
                ticket=response.ticket
            )
            
        except Exception as e:
            logger.error(f"Error creating response notification: {e}")
    
    def _create_assignment_notification(self, ticket: SupportTicket, agent: "CustomUser", assigned_by: "CustomUser" = None):
        """Create notification for ticket assignment"""
        try:
            SupportNotification.objects.create(
                recipient=agent,
                notification_type='ticket_updated',
                title=f"Ticket Assigned: {ticket.ticket_number}",
                message=f"You have been assigned to ticket: {ticket.subject}",
                ticket=ticket
            )
            
        except Exception as e:
            logger.error(f"Error creating assignment notification: {e}")
    
    def _create_escalation_notification(self, ticket: SupportTicket, escalated_by: User = None):
        """Create notification for ticket escalation"""
        try:
            # Notify management/senior staff
            senior_staff = User.objects.filter(is_staff=True, is_superuser=True, is_active=True)
            
            for staff_user in senior_staff:
                SupportNotification.objects.create(
                    recipient=staff_user,
                    notification_type='ticket_updated',
                    title=f"Ticket Escalated: {ticket.ticket_number}",
                    message=f"Ticket has been escalated: {ticket.subject}",
                    ticket=ticket
                )
                
        except Exception as e:
            logger.error(f"Error creating escalation notification: {e}")
    
    def _send_ticket_email_notification(self, ticket: SupportTicket, event_type: str):
        """Send email notification for ticket events"""
        try:
            if not getattr(settings, 'EMAIL_NOTIFICATIONS_ENABLED', False):
                return
            
            subject_map = {
                'created': f"Support Ticket Created: {ticket.ticket_number}",
                'closed': f"Support Ticket Resolved: {ticket.ticket_number}",
            }
            
            subject = subject_map.get(event_type, f"Support Ticket Update: {ticket.ticket_number}")
            
            message = f"""
            Dear {ticket.user.get_full_name() or ticket.user.phone_number},
            
            Your support ticket has been {event_type}.
            
            Ticket Number: {ticket.ticket_number}
            Subject: {ticket.subject}
            Status: {ticket.get_status_display()}
            
            You can view your ticket at: {settings.SITE_URL}{ticket.get_absolute_url()}
            
            Best regards,
            Betzide Support Team
            """
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[ticket.user.email],
                fail_silently=True
            )
            
        except Exception as e:
            logger.error(f"Error sending ticket email notification: {e}")
    
    def _send_response_email_notification(self, response: TicketResponse, recipient: "CustomUser"):
        """Send email notification for ticket responses"""
        try:
            if not getattr(settings, 'EMAIL_NOTIFICATIONS_ENABLED', False):
                return
            
            if not recipient.email:
                return
            
            subject = f"New Response to Ticket: {response.ticket.ticket_number}"
            
            message = f"""
            Dear {recipient.get_full_name() or recipient.phone_number},
            
            A new response has been added to your support ticket.
            
            Ticket Number: {response.ticket.ticket_number}
            Subject: {response.ticket.subject}
            
            Response:
            {response.message}
            
            You can view the full conversation at: {settings.SITE_URL}{response.ticket.get_absolute_url()}
            
            Best regards,
            Betzide Support Team
            """
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient.email],
                fail_silently=True
            )
            
        except Exception as e:
            logger.error(f"Error sending response email notification: {e}")


class FAQService:
    """Service for managing FAQ articles"""

    def search_articles(self, query: str, category_id: str = None) -> List[FAQArticle]:
        """
        Search FAQ articles

        Args:
            query: Search query
            category_id: Optional category filter

        Returns:
            List[FAQArticle]: Matching articles
        """
        try:
            articles = FAQArticle.objects.filter(is_published=True)

            if category_id:
                articles = articles.filter(category_id=category_id)

            if query:
                articles = articles.filter(
                    Q(question__icontains=query) |
                    Q(answer__icontains=query) |
                    Q(keywords__icontains=query)
                )

            return articles.order_by('-view_count', 'sort_order')

        except Exception as e:
            logger.error(f"Error searching FAQ articles: {e}")
            return []

    def get_popular_articles(self, limit: int = 10) -> List[FAQArticle]:
        """Get most popular FAQ articles"""
        try:
            return FAQArticle.objects.filter(
                is_published=True
            ).order_by('-view_count')[:limit]

        except Exception as e:
            logger.error(f"Error getting popular articles: {e}")
            return []

    def get_articles_by_category(self, category_id: str) -> List[FAQArticle]:
        """Get articles by category"""
        try:
            return FAQArticle.objects.filter(
                category_id=category_id,
                is_published=True
            ).order_by('sort_order', 'question')

        except Exception as e:
            logger.error(f"Error getting articles by category: {e}")
            return []

    def vote_article(self, article_id: str, is_helpful: bool) -> bool:
        """Vote on article helpfulness"""
        try:
            article = FAQArticle.objects.get(id=article_id, is_published=True)

            if is_helpful:
                article.vote_helpful()
            else:
                article.vote_unhelpful()

            return True

        except FAQArticle.DoesNotExist:
            logger.error(f"Article {article_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error voting on article: {e}")
            return False


class ChatService:
    """Service for managing live chat sessions"""

    def start_chat_session(self, user: "CustomUser", subject: str = '') -> ChatSession:
        """
        Start a new chat session

        Args:
            user: User starting the chat
            subject: Optional chat subject

        Returns:
            ChatSession: Created session
        """
        try:
            with transaction.atomic():
                # Check if user has an active session
                existing_session = ChatSession.objects.filter(
                    user=user,
                    status__in=['waiting', 'active']
                ).first()

                if existing_session:
                    return existing_session

                # Create new session
                session = ChatSession.objects.create(
                    user=user,
                    subject=subject,
                    status='waiting'
                )

                # Add system message
                self.add_system_message(
                    session,
                    "Welcome to Betzide Support! An agent will be with you shortly."
                )

                # Notify available agents
                self._notify_available_agents(session)

                logger.info(f"Chat session started: {session.session_id} by user {user.id}")
                return session

        except Exception as e:
            logger.error(f"Error starting chat session: {e}")
            raise

    def assign_agent_to_chat(self, session: ChatSession, agent: "CustomUser") -> bool:
        """
        Assign an agent to a chat session

        Args:
            session: Chat session
            agent: Agent to assign

        Returns:
            bool: Success status
        """
        try:
            if not agent.is_staff:
                raise ValueError("Only staff members can be assigned to chats")

            session.assign_agent(agent)

            # Add system message
            self.add_system_message(
                session,
                f"Agent {agent.get_full_name() or agent.username} has joined the chat."
            )

            # Create notification
            self._create_chat_notification(session, 'chat_started', session.user)

            logger.info(f"Agent {agent.id} assigned to chat {session.session_id}")
            return True

        except Exception as e:
            logger.error(f"Error assigning agent to chat: {e}")
            return False

    def send_message(self, session: ChatSession, sender: "CustomUser", content: str,
                    message_type: str = 'text', attachment=None) -> ChatMessage:
        """
        Send a message in a chat session

        Args:
            session: Chat session
            sender: Message sender
            content: Message content
            message_type: Type of message
            attachment: Optional file attachment

        Returns:
            ChatMessage: Created message
        """
        try:
            with transaction.atomic():
                # Create message
                message = ChatMessage.objects.create(
                    session=session,
                    sender=sender,
                    content=content,
                    message_type=message_type,
                    attachment=attachment
                )

                # Update session activity
                session.last_activity_at = timezone.now()
                session.save(update_fields=['last_activity_at'])

                # Create notification for recipient
                recipient = session.agent if sender == session.user else session.user
                if recipient:
                    self._create_chat_notification(session, 'chat_message', recipient)

                logger.info(f"Message sent in chat {session.session_id} by user {sender.id}")
                return message

        except Exception as e:
            logger.error(f"Error sending chat message: {e}")
            raise

    def add_system_message(self, session: ChatSession, content: str) -> ChatMessage:
        """
        Add a system message to a chat session

        Args:
            session: Chat session
            content: Message content

        Returns:
            ChatMessage: Created system message
        """
        try:
            # Use the first staff user as sender for system messages
            system_user = User.objects.filter(is_staff=True).first()
            if not system_user:
                raise ValueError("No staff user available for system messages")

            message = ChatMessage.objects.create(
                session=session,
                sender=system_user,
                content=content,
                message_type='system',
                is_system_message=True
            )

            return message

        except Exception as e:
            logger.error(f"Error adding system message: {e}")
            raise

    def end_chat_session(self, session: ChatSession, ended_by: "CustomUser") -> bool:
        """
        End a chat session

        Args:
            session: Chat session to end
            ended_by: User ending the session

        Returns:
            bool: Success status
        """
        try:
            session.end_session(ended_by=ended_by)

            # Add system message
            self.add_system_message(
                session,
                "Chat session has been ended. Thank you for contacting Betzide Support!"
            )

            # Create notification
            other_user = session.user if ended_by == session.agent else session.agent
            if other_user:
                self._create_chat_notification(session, 'chat_ended', other_user)

            logger.info(f"Chat session {session.session_id} ended by user {ended_by.id}")
            return True

        except Exception as e:
            logger.error(f"Error ending chat session: {e}")
            return False

    def get_user_chat_history(self, user: "CustomUser", limit: int = 10) -> List[ChatSession]:
        """Get user's chat history"""
        try:
            return ChatSession.objects.filter(
                user=user
            ).order_by('-started_at')[:limit]

        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return []

    def get_agent_active_chats(self, agent: "CustomUser") -> List[ChatSession]:
        """Get agent's active chat sessions"""
        try:
            return ChatSession.objects.filter(
                agent=agent,
                status='active'
            ).order_by('-last_activity_at')

        except Exception as e:
            logger.error(f"Error getting agent active chats: {e}")
            return []

    def get_waiting_chats(self) -> List[ChatSession]:
        """Get chat sessions waiting for agents"""
        try:
            return ChatSession.objects.filter(
                status='waiting'
            ).order_by('started_at')

        except Exception as e:
            logger.error(f"Error getting waiting chats: {e}")
            return []

    def _notify_available_agents(self, session: ChatSession):
        """Notify available agents about new chat"""
        try:
            # Get available staff members
            available_agents = User.objects.filter(
                is_staff=True,
                is_active=True
            )

            for agent in available_agents:
                SupportNotification.objects.create(
                    recipient=agent,
                    notification_type='chat_started',
                    title="New Chat Session",
                    message=f"A new chat session is waiting for an agent: {session.session_id}",
                    chat_session=session
                )

        except Exception as e:
            logger.error(f"Error notifying available agents: {e}")

    def _create_chat_notification(self, session: ChatSession, notification_type: str, recipient: "CustomUser"):
        """Create notification for chat events"""
        try:
            title_map = {
                'chat_started': "Chat Session Started",
                'chat_message': "New Chat Message",
                'chat_ended': "Chat Session Ended",
            }

            message_map = {
                'chat_started': f"Your chat session {session.session_id} has started.",
                'chat_message': f"You have a new message in chat {session.session_id}.",
                'chat_ended': f"Your chat session {session.session_id} has ended.",
            }

            SupportNotification.objects.create(
                recipient=recipient,
                notification_type=notification_type,
                title=title_map.get(notification_type, "Chat Update"),
                message=message_map.get(notification_type, "Chat session update"),
                chat_session=session
            )

        except Exception as e:
            logger.error(f"Error creating chat notification: {e}")
